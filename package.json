{"type": "module", "main": "./dist/cjs/bundle.cjs", "exports": {".": {"import": "./dist/esm/bundle.mjs", "require": "./dist/cjs/bundle.cjs", "default": "./dist/esm/bundle.cjs"}}, "module": "./dist/esm/bundle.js", "name": "nhentai-api", "version": "3.4.3", "description": "nHentai Node.JS API client.", "keywords": ["r18", "adult", "nhentai", "api"], "homepage": "https://zekfad.github.io/nhentai-api/", "bugs": {"url": "https://github.com/Zekfad/nhentai-api/issues", "email": "<EMAIL>"}, "license": "ISC", "author": "Zekfad <<EMAIL>> (https://zekfad.znnme.eu.org)", "repository": "github:Zekfad/nhentai-api", "types": "./types/index.d.ts", "scripts": {"lint": "eslint . --ext .js,.cjs,.mjs", "dev": "rollup -c --watch --environment mode:dev", "build": "run-p build:**", "build:src": "rollup -c", "build:docs": "webdoc --config .webdocrc.json --site-root /nhentai-api --verbose", "build:def": "tsc", "test": "nyc --reporter=lcov mocha", "coverage": "codecov"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/eslint-parser": "^7.16.5", "@babel/plugin-proposal-class-properties": "^7.16.7", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-json": "^4.0.2", "@rollup/plugin-node-resolve": "^13.1.3", "@webdoc/cli": "^1.5.5", "@zekfad/eslint-config": "^1.0.2", "codecov": "^3.8.3", "eslint": "^8.7.0", "eslint-plugin-import": "^2.25.4", "magic-string": "^0.25.7", "mocha": "^9.1.4", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "rollup": "^2.64.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.5.4"}, "engines": {"node": ">=22"}, "resolutions": {"**/micromatch": "^4.0.4"}, "dependencies": {"@sparticuz/chromium": "^138.0.2", "puppeteer": "^24.17.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}}